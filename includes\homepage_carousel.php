<?php
/**
 * مكون الكاروسيل للصفحة الرئيسية
 * Homepage Carousel Component
 */

// جلب إعدادات الكاروسيل
$carouselSettings = getHomepageSectionSettings('carousel');

// القيم الافتراضية
$defaults = [
    'slide_1_image' => 'https://via.placeholder.com/1200x600/667eea/ffffff?text=الشريحة+الأولى',
    'slide_1_title' => 'مرحباً بك في متجرنا الإلكتروني',
    'slide_1_subtitle' => 'اكتشف مجموعة واسعة من المنتجات عالية الجودة',
    'slide_1_button_text' => 'تصفح المنتجات',
    'slide_1_button_url' => '/products.php',
    'slide_2_image' => 'https://via.placeholder.com/1200x600/764ba2/ffffff?text=الشريحة+الثانية',
    'slide_3_image' => 'https://via.placeholder.com/1200x600/28a745/ffffff?text=الشريحة+الثالثة',
    'slide_4_image' => 'https://via.placeholder.com/1200x600/dc3545/ffffff?text=الشريحة+الرابعة',
    'auto_advance_time' => '10',
    'show_indicators' => '1',
    'show_controls' => '1'
];

// دمج الإعدادات مع القيم الافتراضية
foreach ($defaults as $key => $value) {
    if (!isset($carouselSettings[$key]) || empty($carouselSettings[$key])) {
        $carouselSettings[$key] = $value;
    }
}

$autoAdvanceTime = (int)$carouselSettings['auto_advance_time'] * 1000; // تحويل إلى ميلي ثانية
$showIndicators = $carouselSettings['show_indicators'] == '1';
$showControls = $carouselSettings['show_controls'] == '1';
?>

<!-- Hero Carousel Section -->
<section class="hero-carousel-section">
    <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="<?php echo $autoAdvanceTime; ?>">
        
        <?php if ($showIndicators): ?>
        <!-- Carousel Indicators -->
        <div class="carousel-indicators">
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="1"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="2"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="3"></button>
        </div>
        <?php endif; ?>

        <!-- Carousel Inner -->
        <div class="carousel-inner">
            <!-- Slide 1 - With Text Overlay -->
            <div class="carousel-item active">
                <img src="<?php echo htmlspecialchars($carouselSettings['slide_1_image']); ?>" 
                     class="d-block w-100 carousel-image" 
                     alt="<?php echo htmlspecialchars($carouselSettings['slide_1_title']); ?>">
                <div class="carousel-caption-overlay">
                    <div class="container">
                        <div class="row align-items-center min-vh-50">
                            <div class="col-lg-6 text-start">
                                <h1 class="display-4 fw-bold mb-4 text-white">
                                    <?php echo htmlspecialchars($carouselSettings['slide_1_title']); ?>
                                </h1>
                                <p class="lead mb-4 text-white">
                                    <?php echo htmlspecialchars($carouselSettings['slide_1_subtitle']); ?>
                                </p>
                                <div class="d-flex gap-3">
                                    <a href="<?php echo SITE_URL . htmlspecialchars($carouselSettings['slide_1_button_url']); ?>" 
                                       class="btn btn-light btn-lg">
                                        <i class="bi bi-grid"></i> 
                                        <?php echo htmlspecialchars($carouselSettings['slide_1_button_text']); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2 - Image Only -->
            <div class="carousel-item">
                <img src="<?php echo htmlspecialchars($carouselSettings['slide_2_image']); ?>" 
                     class="d-block w-100 carousel-image" 
                     alt="الشريحة الثانية">
            </div>

            <!-- Slide 3 - Image Only -->
            <div class="carousel-item">
                <img src="<?php echo htmlspecialchars($carouselSettings['slide_3_image']); ?>" 
                     class="d-block w-100 carousel-image" 
                     alt="الشريحة الثالثة">
            </div>

            <!-- Slide 4 - Image Only -->
            <div class="carousel-item">
                <img src="<?php echo htmlspecialchars($carouselSettings['slide_4_image']); ?>" 
                     class="d-block w-100 carousel-image" 
                     alt="الشريحة الرابعة">
            </div>
        </div>

        <?php if ($showControls): ?>
        <!-- Carousel Controls -->
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon"></span>
            <span class="visually-hidden">السابق</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon"></span>
            <span class="visually-hidden">التالي</span>
        </button>
        <?php endif; ?>
    </div>
</section>

<style>
.hero-carousel-section {
    position: relative;
    margin-bottom: 40px;
}

.carousel-image {
    height: 600px;
    object-fit: cover;
    object-position: center;
}

.carousel-caption-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%);
    display: flex;
    align-items: center;
    z-index: 2;
}

.min-vh-50 {
    min-height: 50vh;
}

.carousel-indicators {
    bottom: 20px;
    z-index: 3;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
    background-color: rgba(255,255,255,0.5);
    border: 2px solid rgba(255,255,255,0.8);
    transition: all 0.3s ease;
}

.carousel-indicators button.active {
    background-color: #fff;
    transform: scale(1.2);
}

.carousel-control-prev,
.carousel-control-next {
    width: 60px;
    height: 60px;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0,0,0,0.5);
    border-radius: 50%;
    border: none;
    transition: all 0.3s ease;
}

.carousel-control-prev {
    left: 20px;
}

.carousel-control-next {
    right: 20px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    background-color: rgba(0,0,0,0.8);
    transform: translateY(-50%) scale(1.1);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 24px;
    height: 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .carousel-image {
        height: 400px;
    }
    
    .carousel-caption-overlay h1 {
        font-size: 2rem;
    }
    
    .carousel-caption-overlay .lead {
        font-size: 1rem;
    }
    
    .carousel-control-prev,
    .carousel-control-next {
        width: 40px;
        height: 40px;
    }
    
    .carousel-control-prev {
        left: 10px;
    }
    
    .carousel-control-next {
        right: 10px;
    }
    
    .carousel-control-prev-icon,
    .carousel-control-next-icon {
        width: 16px;
        height: 16px;
    }
}

@media (max-width: 576px) {
    .carousel-image {
        height: 300px;
    }
    
    .carousel-caption-overlay {
        padding: 20px 0;
    }
    
    .carousel-caption-overlay h1 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .carousel-caption-overlay .lead {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize carousel with custom settings
    const carousel = document.getElementById('heroCarousel');
    if (carousel) {
        const bsCarousel = new bootstrap.Carousel(carousel, {
            interval: <?php echo $autoAdvanceTime; ?>,
            wrap: true,
            touch: true
        });
        
        // Pause on hover
        carousel.addEventListener('mouseenter', function() {
            bsCarousel.pause();
        });
        
        carousel.addEventListener('mouseleave', function() {
            bsCarousel.cycle();
        });
    }
});
</script>
