<?php
/**
 * مكون الكاروسيل للصفحة الرئيسية - محسن ومطور
 * Enhanced Homepage Carousel Component
 */

// جلب إعدادات الكاروسيل
$carouselSettings = getHomepageSectionSettings('carousel');

// القيم الافتراضية المحسنة
$defaults = [
    'slide_1_image' => '',
    'slide_1_title' => 'مرحباً بك في متجرنا الإلكتروني',
    'slide_1_subtitle' => 'اكتشف مجموعة واسعة من المنتجات عالية الجودة بأفضل الأسعار',
    'slide_1_button_text' => 'تصفح المنتجات',
    'slide_1_button_url' => '/products.php',
    'slide_2_image' => '',
    'slide_2_title' => '',
    'slide_2_subtitle' => '',
    'slide_3_image' => '',
    'slide_3_title' => '',
    'slide_3_subtitle' => '',
    'slide_4_image' => '',
    'slide_4_title' => '',
    'slide_4_subtitle' => '',
    'auto_advance_time' => '8',
    'show_indicators' => '1',
    'show_controls' => '1'
];

// دمج الإعدادات مع القيم الافتراضية
foreach ($defaults as $key => $value) {
    if (!isset($carouselSettings[$key]) || empty($carouselSettings[$key])) {
        $carouselSettings[$key] = $value;
    }
}

// التحقق من وجود شرائح فعلية
$hasSlides = false;
$activeSlides = [];
for ($i = 1; $i <= 4; $i++) {
    if (!empty($carouselSettings["slide_{$i}_image"])) {
        $hasSlides = true;
        $activeSlides[] = $i;
    }
}

$autoAdvanceTime = (int)$carouselSettings['auto_advance_time'] * 1000; // تحويل إلى ميلي ثانية
$showIndicators = $carouselSettings['show_indicators'] == '1';
$showControls = $carouselSettings['show_controls'] == '1';
?>

<!-- Hero Carousel Section -->
<section class="hero-carousel-section">
    <?php if ($hasSlides): ?>
        <!-- Active Carousel with Images -->
        <div id="heroCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel" data-bs-interval="<?php echo $autoAdvanceTime; ?>">

            <?php if ($showIndicators && count($activeSlides) > 1): ?>
            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <?php foreach ($activeSlides as $index => $slideNum): ?>
                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="<?php echo $index; ?>"
                            <?php echo $index === 0 ? 'class="active"' : ''; ?>></button>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <!-- Carousel Inner -->
            <div class="carousel-inner">
                <?php foreach ($activeSlides as $index => $slideNum): ?>
                    <div class="carousel-item <?php echo $index === 0 ? 'active' : ''; ?>">
                        <!-- Loading Skeleton -->
                        <div class="carousel-image-skeleton"></div>

                        <!-- Main Image -->
                        <img src="<?php echo htmlspecialchars($carouselSettings["slide_{$slideNum}_image"]); ?>"
                             class="d-block w-100 carousel-image"
                             alt="<?php echo htmlspecialchars($carouselSettings["slide_{$slideNum}_title"] ?: "شريحة $slideNum"); ?>"
                             loading="<?php echo $index === 0 ? 'eager' : 'lazy'; ?>"
                             onload="this.parentElement.querySelector('.carousel-image-skeleton').style.display='none'">

                        <!-- Text Overlay (only if title or subtitle exists) -->
                        <?php if (!empty($carouselSettings["slide_{$slideNum}_title"]) || !empty($carouselSettings["slide_{$slideNum}_subtitle"])): ?>
                        <div class="carousel-caption-overlay">
                            <div class="container">
                                <div class="row align-items-center min-vh-50">
                                    <div class="col-lg-8 col-xl-6 text-end">
                                        <?php if (!empty($carouselSettings["slide_{$slideNum}_title"])): ?>
                                            <h1 class="carousel-title animate-slide-up">
                                                <?php echo htmlspecialchars($carouselSettings["slide_{$slideNum}_title"]); ?>
                                            </h1>
                                        <?php endif; ?>

                                        <?php if (!empty($carouselSettings["slide_{$slideNum}_subtitle"])): ?>
                                            <p class="carousel-subtitle animate-slide-up-delay">
                                                <?php echo htmlspecialchars($carouselSettings["slide_{$slideNum}_subtitle"]); ?>
                                            </p>
                                        <?php endif; ?>

                                        <?php if ($slideNum == 1 && !empty($carouselSettings['slide_1_button_text'])): ?>
                                            <div class="carousel-actions animate-fade-in-delay">
                                                <a href="<?php echo SITE_URL . htmlspecialchars($carouselSettings['slide_1_button_url']); ?>"
                                                   class="btn btn-primary btn-lg carousel-btn">
                                                    <i class="bi bi-grid-fill me-2"></i>
                                                    <?php echo htmlspecialchars($carouselSettings['slide_1_button_text']); ?>
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <?php if ($showControls && count($activeSlides) > 1): ?>
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon"></span>
                <span class="visually-hidden">السابق</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon"></span>
                <span class="visually-hidden">التالي</span>
            </button>
            <?php endif; ?>
        </div>

    <?php else: ?>
        <!-- Default Fallback Hero Section -->
        <div class="hero-default-section">
            <div class="hero-default-background">
                <div class="hero-gradient-overlay"></div>
                <div class="container">
                    <div class="row align-items-center min-vh-60">
                        <div class="col-lg-8 col-xl-6 text-end">
                            <h1 class="hero-default-title animate-slide-up">
                                <?php echo htmlspecialchars($carouselSettings['slide_1_title']); ?>
                            </h1>
                            <p class="hero-default-subtitle animate-slide-up-delay">
                                <?php echo htmlspecialchars($carouselSettings['slide_1_subtitle']); ?>
                            </p>
                            <div class="hero-default-actions animate-fade-in-delay">
                                <a href="<?php echo SITE_URL . htmlspecialchars($carouselSettings['slide_1_button_url']); ?>"
                                   class="btn btn-primary btn-lg me-3">
                                    <i class="bi bi-grid-fill me-2"></i>
                                    <?php echo htmlspecialchars($carouselSettings['slide_1_button_text']); ?>
                                </a>
                                <a href="<?php echo SITE_URL; ?>/about.php" class="btn btn-outline-light btn-lg">
                                    <i class="bi bi-info-circle me-2"></i>
                                    تعرف علينا
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</section>

<style>
/* Hero Carousel Section - Enhanced Professional Design */
.hero-carousel-section {
    position: relative;
    margin-bottom: 50px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    border-radius: 0 0 20px 20px;
    overflow: hidden;
}

/* Carousel Fade Effect */
.carousel-fade .carousel-item {
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
}

.carousel-fade .carousel-item.active {
    opacity: 1;
}

/* Image Styling */
.carousel-image {
    height: 650px;
    width: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.carousel-item:hover .carousel-image {
    transform: scale(1.02);
}

/* Loading Skeleton */
.carousel-image-skeleton {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    z-index: 1;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Text Overlay Enhancements */
.carousel-caption-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.2) 100%);
    display: flex;
    align-items: center;
    z-index: 2;
}

.min-vh-50 {
    min-height: 50vh;
}

.min-vh-60 {
    min-height: 60vh;
}

/* Typography Enhancements */
.carousel-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #fff;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.carousel-subtitle {
    font-size: 1.4rem;
    color: rgba(255,255,255,0.95);
    text-shadow: 1px 1px 4px rgba(0,0,0,0.5);
    margin-bottom: 2rem;
    line-height: 1.6;
    font-weight: 400;
}

.carousel-actions {
    margin-top: 2rem;
}

.carousel-btn {
    padding: 15px 35px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.carousel-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.3);
}

/* Indicators Enhancement */
.carousel-indicators {
    bottom: 30px;
    z-index: 3;
}

.carousel-indicators button {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin: 0 8px;
    background-color: rgba(255,255,255,0.4);
    border: 3px solid rgba(255,255,255,0.7);
    transition: all 0.4s ease;
    opacity: 0.8;
}

.carousel-indicators button.active {
    background-color: #fff;
    transform: scale(1.3);
    opacity: 1;
    box-shadow: 0 4px 15px rgba(255,255,255,0.3);
}

/* Controls Enhancement */
.carousel-control-prev,
.carousel-control-next {
    width: 70px;
    height: 70px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, rgba(0,0,0,0.6), rgba(0,0,0,0.4));
    border-radius: 50%;
    border: 3px solid rgba(255,255,255,0.2);
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
}

.carousel-control-prev {
    left: 30px;
}

.carousel-control-next {
    right: 30px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    background: linear-gradient(135deg, rgba(0,0,0,0.8), rgba(0,0,0,0.6));
    transform: translateY(-50%) scale(1.1);
    border-color: rgba(255,255,255,0.4);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 28px;
    height: 28px;
}

/* Default Hero Section (Fallback) */
.hero-default-section {
    position: relative;
    min-height: 650px;
    display: flex;
    align-items: center;
}

.hero-default-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.hero-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
}

.hero-default-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #fff;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.3);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-default-subtitle {
    font-size: 1.4rem;
    color: rgba(255,255,255,0.95);
    text-shadow: 1px 1px 4px rgba(0,0,0,0.3);
    margin-bottom: 2.5rem;
    line-height: 1.6;
    font-weight: 400;
}

.hero-default-actions {
    margin-top: 2rem;
}

/* Animation Classes */
.animate-slide-up {
    animation: slideUp 1s ease-out;
}

.animate-slide-up-delay {
    animation: slideUp 1s ease-out 0.3s both;
}

.animate-fade-in-delay {
    animation: fadeInUp 1s ease-out 0.6s both;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .carousel-title,
    .hero-default-title {
        font-size: 3rem;
    }

    .carousel-subtitle,
    .hero-default-subtitle {
        font-size: 1.2rem;
    }
}

@media (max-width: 768px) {
    .hero-carousel-section {
        margin-bottom: 30px;
        border-radius: 0 0 15px 15px;
    }

    .carousel-image,
    .hero-default-section {
        height: 450px;
    }

    .carousel-title,
    .hero-default-title {
        font-size: 2.2rem;
        margin-bottom: 1rem;
    }

    .carousel-subtitle,
    .hero-default-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 50px;
        height: 50px;
    }

    .carousel-control-prev {
        left: 15px;
    }

    .carousel-control-next {
        right: 15px;
    }

    .carousel-control-prev-icon,
    .carousel-control-next-icon {
        width: 20px;
        height: 20px;
    }

    .carousel-btn {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .carousel-image,
    .hero-default-section {
        height: 350px;
    }

    .carousel-caption-overlay,
    .hero-default-background {
        padding: 20px 0;
    }

    .carousel-title,
    .hero-default-title {
        font-size: 1.8rem;
        margin-bottom: 1rem;
    }

    .carousel-subtitle,
    .hero-default-subtitle {
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
    }

    .carousel-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
        margin-bottom: 10px;
        display: block;
        text-align: center;
    }

    .hero-default-actions .btn {
        display: block;
        margin-bottom: 10px;
        margin-left: 0 !important;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 40px;
        height: 40px;
    }

    .carousel-control-prev {
        left: 10px;
    }

    .carousel-control-next {
        right: 10px;
    }

    .carousel-control-prev-icon,
    .carousel-control-next-icon {
        width: 16px;
        height: 16px;
    }

    .carousel-indicators {
        bottom: 15px;
    }

    .carousel-indicators button {
        width: 10px;
        height: 10px;
        margin: 0 4px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize enhanced carousel
    const carousel = document.getElementById('heroCarousel');
    if (carousel) {
        const bsCarousel = new bootstrap.Carousel(carousel, {
            interval: <?php echo $autoAdvanceTime; ?>,
            wrap: true,
            touch: true,
            pause: 'hover'
        });

        // Enhanced pause/resume functionality
        carousel.addEventListener('mouseenter', function() {
            bsCarousel.pause();
        });

        carousel.addEventListener('mouseleave', function() {
            bsCarousel.cycle();
        });

        // Add slide event listeners for animations
        carousel.addEventListener('slide.bs.carousel', function(e) {
            // Add fade-out animation to current slide
            const activeSlide = carousel.querySelector('.carousel-item.active');
            if (activeSlide) {
                activeSlide.style.opacity = '0.7';
            }
        });

        carousel.addEventListener('slid.bs.carousel', function(e) {
            // Reset animations for new active slide
            const activeSlide = carousel.querySelector('.carousel-item.active');
            if (activeSlide) {
                activeSlide.style.opacity = '1';

                // Re-trigger text animations
                const animatedElements = activeSlide.querySelectorAll('.animate-slide-up, .animate-slide-up-delay, .animate-fade-in-delay');
                animatedElements.forEach(el => {
                    el.style.animation = 'none';
                    el.offsetHeight; // Trigger reflow
                    el.style.animation = null;
                });
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                bsCarousel.prev();
            } else if (e.key === 'ArrowRight') {
                bsCarousel.next();
            }
        });

        // Touch/swipe support enhancement
        let startX = 0;
        let endX = 0;

        carousel.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });

        carousel.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            handleSwipe();
        });

        function handleSwipe() {
            const threshold = 50;
            const diff = startX - endX;

            if (Math.abs(diff) > threshold) {
                if (diff > 0) {
                    bsCarousel.next(); // Swipe left - next slide
                } else {
                    bsCarousel.prev(); // Swipe right - previous slide
                }
            }
        }
    }

    // Initialize default hero animations
    const defaultHero = document.querySelector('.hero-default-section');
    if (defaultHero) {
        // Trigger animations on load
        setTimeout(() => {
            const animatedElements = defaultHero.querySelectorAll('.animate-slide-up, .animate-slide-up-delay, .animate-fade-in-delay');
            animatedElements.forEach(el => {
                el.style.opacity = '1';
            });
        }, 100);
    }

    // Image loading optimization
    const carouselImages = document.querySelectorAll('.carousel-image');
    carouselImages.forEach((img, index) => {
        img.addEventListener('load', function() {
            // Hide skeleton loader
            const skeleton = this.parentElement.querySelector('.carousel-image-skeleton');
            if (skeleton) {
                skeleton.style.opacity = '0';
                setTimeout(() => {
                    skeleton.style.display = 'none';
                }, 300);
            }
        });

        // Error handling for broken images
        img.addEventListener('error', function() {
            console.warn('Failed to load carousel image:', this.src);
            const skeleton = this.parentElement.querySelector('.carousel-image-skeleton');
            if (skeleton) {
                skeleton.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666; font-size: 1.2rem;"><i class="bi bi-image" style="font-size: 3rem; margin-bottom: 1rem;"></i><br>فشل في تحميل الصورة</div>';
                skeleton.style.background = '#f8f9fa';
            }
        });
    });
});
</script>
