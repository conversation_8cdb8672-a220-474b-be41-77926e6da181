<?php
/**
 * Professional Featured Products Test Page
 * Tests the enhanced featured products section
 */

$pageTitle = 'اختبار قسم المنتجات المميزة المحسن';
require_once 'config/config.php';

// Test scenarios with different product configurations
$testScenarios = [
    'normal' => [
        'name' => 'منتجات عادية',
        'description' => 'اختبار عرض المنتجات بدون خصومات مع صور مختلفة',
        'products' => [
            [
                'id' => 1,
                'name' => 'منتج تجريبي أول',
                'short_description' => 'وصف تجريبي للمنتج الأول يحتوي على تفاصيل مهمة حول المنتج وخصائصه المميزة',
                'price' => 150000,
                'discount' => 0,
                'category_name' => 'الإلكترونيات',
                'image_url_1' => 'https://picsum.photos/400/300?random=1',
                'image' => ''
            ],
            [
                'id' => 2,
                'name' => 'منتج تجريبي ثاني',
                'short_description' => 'وصف تجريبي للمنتج الثاني مع معلومات شاملة',
                'price' => 75000,
                'discount' => 0,
                'category_name' => 'الأزياء',
                'image_url_1' => 'https://picsum.photos/400/300?random=2',
                'image' => ''
            ],
            [
                'id' => 3,
                'name' => 'منتج تجريبي ثالث',
                'short_description' => 'وصف مفصل للمنتج الثالث يوضح جميع المميزات والفوائد',
                'price' => 200000,
                'discount' => 0,
                'category_name' => 'المنزل والحديقة',
                'image_url_1' => 'https://picsum.photos/400/300?random=3',
                'image' => ''
            ]
        ]
    ],
    'discounted' => [
        'name' => 'منتجات مخفضة',
        'description' => 'اختبار عرض المنتجات مع خصومات مختلفة',
        'products' => [
            [
                'id' => 4,
                'name' => 'منتج مخفض 20%',
                'short_description' => 'منتج رائع بخصم مميز لفترة محدودة فقط',
                'price' => 100000,
                'discount' => 20,
                'category_name' => 'الإلكترونيات',
                'image_url_1' => 'https://picsum.photos/400/300?random=4',
                'image' => ''
            ],
            [
                'id' => 5,
                'name' => 'منتج مخفض 35%',
                'short_description' => 'عرض خاص بخصم كبير على هذا المنتج المميز',
                'price' => 180000,
                'discount' => 35,
                'category_name' => 'الرياضة',
                'image_url_1' => 'https://picsum.photos/400/300?random=5',
                'image' => ''
            ],
            [
                'id' => 6,
                'name' => 'منتج مخفض 50%',
                'short_description' => 'خصم هائل على هذا المنتج الرائع - عرض محدود',
                'price' => 250000,
                'discount' => 50,
                'category_name' => 'الجمال والعناية',
                'image_url_1' => 'https://picsum.photos/400/300?random=6',
                'image' => ''
            ]
        ]
    ],
    'mixed' => [
        'name' => 'منتجات مختلطة',
        'description' => 'اختبار مزيج من المنتجات العادية والمخفضة مع صور مختلفة',
        'products' => [
            [
                'id' => 7,
                'name' => 'منتج بصورة طويلة',
                'short_description' => 'اختبار عرض منتج بصورة ذات نسبة عرض إلى ارتفاع مختلفة',
                'price' => 120000,
                'discount' => 15,
                'category_name' => 'الكتب',
                'image_url_1' => 'https://picsum.photos/300/500?random=7',
                'image' => ''
            ],
            [
                'id' => 8,
                'name' => 'منتج بصورة عريضة',
                'short_description' => 'اختبار عرض منتج بصورة عريضة لاختبار التناسق',
                'price' => 90000,
                'discount' => 0,
                'category_name' => 'الفنون',
                'image_url_1' => 'https://picsum.photos/600/300?random=8',
                'image' => ''
            ],
            [
                'id' => 9,
                'name' => 'منتج بدون صورة',
                'short_description' => 'اختبار عرض منتج بدون صورة لاختبار الحالة الافتراضية',
                'price' => 80000,
                'discount' => 25,
                'category_name' => 'متنوعة',
                'image_url_1' => '',
                'image' => ''
            ]
        ]
    ],
    'long_names' => [
        'name' => 'أسماء طويلة',
        'description' => 'اختبار عرض المنتجات بأسماء وأوصاف طويلة',
        'products' => [
            [
                'id' => 10,
                'name' => 'منتج تجريبي بعنوان طويل جداً يحتوي على كلمات كثيرة لاختبار التصميم المتجاوب',
                'short_description' => 'وصف طويل جداً للمنتج يحتوي على معلومات مفصلة وشاملة حول جميع خصائص ومميزات هذا المنتج الرائع والمتميز الذي يقدم قيمة استثنائية للعملاء ويلبي جميع احتياجاتهم ومتطلباتهم بأفضل جودة ممكنة وبأسعار تنافسية مناسبة لجميع الفئات',
                'price' => 300000,
                'discount' => 40,
                'category_name' => 'فئة بعنوان طويل جداً',
                'image_url_1' => 'https://picsum.photos/400/300?random=10',
                'image' => ''
            ]
        ]
    ]
];

$currentTest = $_GET['test'] ?? 'normal';
$currentScenario = $testScenarios[$currentTest] ?? $testScenarios['normal'];

// Override featured products for testing
$featuredProducts = $currentScenario['products'];
$featuredProductsSettings = [
    'show_section' => '1',
    'section_title' => 'المنتجات المميزة - اختبار محسن',
    'section_subtitle' => 'تصميم احترافي جديد مع عرض كامل للصور وتأثيرات متقدمة'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Enhanced Featured Products CSS -->
    <link href="assets/css/featured-products.css" rel="stylesheet">
    
    <style>
        body {
            direction: rtl;
            text-align: right;
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .test-controls {
            background: white;
            padding: 2rem;
            margin: 2rem 0;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .test-info {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            padding: 2rem;
            margin: 2rem 0;
            border-radius: 15px;
            border-right: 5px solid #667eea;
        }
        
        .feature-list {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 2rem 0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            color: #28a745;
            margin-left: 1rem;
            font-size: 1.3rem;
        }
        
        .btn-test {
            margin: 0.25rem;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .comparison-section {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 2rem 0;
        }
        
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .before-after > div {
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid #e9ecef;
        }
        
        .before {
            background: #fff3cd;
            border-color: #ffc107;
        }
        
        .after {
            background: #d1edff;
            border-color: #0dcaf0;
        }
        
        @media (max-width: 768px) {
            .before-after {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="test-controls">
                    <h1 class="mb-4">
                        <i class="bi bi-star-fill text-warning"></i> 
                        اختبار قسم المنتجات المميزة المحسن
                    </h1>
                    <p class="lead">اختر سيناريو الاختبار لمشاهدة التحسينات المختلفة:</p>
                    
                    <div class="d-flex flex-wrap gap-2 mb-4">
                        <?php foreach ($testScenarios as $key => $scenario): ?>
                            <a href="?test=<?php echo $key; ?>" 
                               class="btn btn-test <?php echo $currentTest === $key ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                <?php echo $scenario['name']; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="test-info">
                    <h4>
                        <i class="bi bi-info-circle text-primary"></i> 
                        السيناريو الحالي: <?php echo $currentScenario['name']; ?>
                    </h4>
                    <p class="mb-0"><?php echo $currentScenario['description']; ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the enhanced featured products section -->
    <?php if (isset($featuredProductsSettings['show_section']) && $featuredProductsSettings['show_section'] == '1' && !empty($featuredProducts)): ?>
    <section class="featured-products-section">
        <div class="featured-products-container">
            <!-- Professional Section Header -->
            <div class="featured-products-header">
                <h2 class="featured-products-title"><?php echo htmlspecialchars($featuredProductsSettings['section_title'] ?? 'المنتجات المميزة'); ?></h2>
                <p class="featured-products-subtitle"><?php echo htmlspecialchars($featuredProductsSettings['section_subtitle'] ?? 'اكتشف أفضل منتجاتنا المختارة بعناية'); ?></p>
            </div>
            
            <!-- Professional Products Grid -->
            <div class="products-grid">
                <?php foreach ($featuredProducts as $product): ?>
                    <div class="professional-product-card">
                        <!-- Discount Badge -->
                        <?php if ($product['discount'] > 0): ?>
                            <div class="professional-discount-badge">
                                خصم <?php echo $product['discount']; ?>%
                            </div>
                        <?php endif; ?>
                        
                        <!-- Professional Image Container with Complete Display -->
                        <div class="product-image-container">
                            <?php
                            $imageUrl = '';
                            $hasImage = false;
                            
                            for ($i = 1; $i <= 5; $i++) {
                                if (!empty($product['image_url_' . $i])) {
                                    $imageUrl = $product['image_url_' . $i];
                                    $hasImage = true;
                                    break;
                                }
                            }
                            
                            if (!$hasImage && !empty($product['image'])) {
                                $imageUrl = UPLOAD_URL . '/' . $product['image'];
                                $hasImage = true;
                            }
                            ?>
                            
                            <?php if ($hasImage): ?>
                                <img src="<?php echo $imageUrl; ?>"
                                     class="product-image" 
                                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     loading="lazy"
                                     onerror="this.parentElement.innerHTML='<div class=\'product-image-error\'><i class=\'bi bi-image\'></i><div>فشل في تحميل الصورة</div></div>'">
                            <?php else: ?>
                                <div class="product-image-error">
                                    <i class="bi bi-image"></i>
                                    <div>لا توجد صورة متاحة</div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Professional Card Content -->
                        <div class="professional-card-content">
                            <!-- Product Category -->
                            <div class="product-category">
                                <i class="bi bi-tag"></i>
                                <?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?>
                            </div>
                            
                            <!-- Product Title -->
                            <h3 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h3>
                            
                            <!-- Product Description -->
                            <p class="product-description">
                                <?php echo htmlspecialchars($product['short_description'] ?? 'لا يوجد وصف متاح'); ?>
                            </p>
                            
                            <!-- Professional Price Section -->
                            <div class="professional-price-section">
                                <div class="price-display">
                                    <?php if ($product['discount'] > 0): ?>
                                        <?php $discountedPrice = $product['price'] - ($product['price'] * $product['discount'] / 100); ?>
                                        <div class="current-price"><?php echo number_format($discountedPrice) . ' د.ع'; ?></div>
                                        <div class="original-price"><?php echo number_format($product['price']) . ' د.ع'; ?></div>
                                    <?php else: ?>
                                        <div class="current-price"><?php echo number_format($product['price']) . ' د.ع'; ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if ($product['discount'] > 0): ?>
                                    <div class="discount-percentage">
                                        وفر <?php echo $product['discount']; ?>%
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Professional Action Buttons -->
                            <div class="professional-card-actions">
                                <a href="#" class="professional-btn btn-view">
                                    <i class="bi bi-eye"></i>
                                    عرض التفاصيل
                                </a>
                                <button class="professional-btn btn-add-cart" onclick="alert('تم إضافة المنتج للسلة!')">
                                    <i class="bi bi-cart-plus"></i>
                                    أضف للسلة
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Professional View All Button -->
            <div class="view-all-container">
                <a href="#" class="btn-view-all">
                    <i class="bi bi-grid-3x3-gap"></i>
                    عرض جميع المنتجات
                </a>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <div class="container mt-5">
        <div class="row">
            <div class="col-lg-8">
                <div class="feature-list">
                    <h4 class="mb-4">
                        <i class="bi bi-check-circle text-success"></i> 
                        التحسينات المطبقة
                    </h4>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>تصميم احترافي جديد:</strong> 
                            قسم محسن بتصميم حديث ومتقدم مع تأثيرات بصرية رائعة
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>عرض كامل للصور:</strong> 
                            إصلاح مشكلة قطع الصور مع عرض كامل للصورة بنسب صحيحة
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>بطاقات منتجات محسنة:</strong> 
                            تصميم بطاقات حديث مع تأثيرات hover وانتقالات سلسة
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>تصميم متجاوب:</strong> 
                            يعمل بشكل مثالي على جميع الأجهزة والشاشات
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>دعم RTL محسن:</strong> 
                            تخطيط عربي محسن مع دعم كامل للكتابة من اليمين لليسار
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>معالجة الأخطاء:</strong> 
                            حالات تحميل وأخطاء محسنة مع رسائل واضحة
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>أداء محسن:</strong> 
                            تحميل كسول للصور وتحسينات في الأداء
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="feature-list">
                    <h5 class="mb-3">
                        <i class="bi bi-palette"></i> 
                        مميزات التصميم
                    </h5>
                    
                    <div class="mb-3">
                        <strong>الألوان:</strong> 
                        لوحة ألوان احترافية متناسقة
                    </div>
                    
                    <div class="mb-3">
                        <strong>الخطوط:</strong> 
                        خط Cairo العربي الاحترافي
                    </div>
                    
                    <div class="mb-3">
                        <strong>الظلال:</strong> 
                        ظلال متدرجة وناعمة
                    </div>
                    
                    <div class="mb-3">
                        <strong>الانتقالات:</strong> 
                        تأثيرات سلسة ومتقدمة
                    </div>
                    
                    <div class="mb-3">
                        <strong>التخطيط:</strong> 
                        شبكة مرنة ومتجاوبة
                    </div>
                </div>
                
                <div class="comparison-section">
                    <h5 class="mb-3">
                        <i class="bi bi-arrow-left-right"></i> 
                        مقارنة التحسينات
                    </h5>
                    
                    <div class="before-after">
                        <div class="before">
                            <h6>قبل التحسين</h6>
                            <ul class="small">
                                <li>تصميم بسيط</li>
                                <li>قطع في الصور</li>
                                <li>بطاقات عادية</li>
                                <li>تأثيرات محدودة</li>
                            </ul>
                        </div>
                        <div class="after">
                            <h6>بعد التحسين</h6>
                            <ul class="small">
                                <li>تصميم احترافي</li>
                                <li>عرض كامل للصور</li>
                                <li>بطاقات متقدمة</li>
                                <li>تأثيرات رائعة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
