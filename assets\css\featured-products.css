/**
 * Professional Featured Products Section Styles
 * Modern, Clean, and Professional Implementation
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

/* ==========================================================================
   Featured Products Section Container
   ========================================================================== */

.featured-products-section {
    position: relative;
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    margin: 3rem 0;
    overflow: hidden;
}

.featured-products-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="%23ffffff" opacity="0.05"/><circle cx="10" cy="60" r="0.5" fill="%23ffffff" opacity="0.05"/><circle cx="90" cy="40" r="0.5" fill="%23ffffff" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.featured-products-container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* ==========================================================================
   Section Header
   ========================================================================== */

.featured-products-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
}

.featured-products-header::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.featured-products-title {
    font-size: 3rem;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    position: relative;
}

.featured-products-subtitle {
    font-size: 1.3rem;
    color: #6c757d;
    font-weight: 400;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* ==========================================================================
   Product Grid
   ========================================================================== */

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* ==========================================================================
   Professional Product Cards
   ========================================================================== */

.professional-product-card {
    background: #ffffff;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
}

.professional-product-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 50px rgba(0,0,0,0.2);
}

.professional-product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.professional-product-card:hover::before {
    opacity: 1;
}

/* ==========================================================================
   Image Container with Complete Display
   ========================================================================== */

.product-image-container {
    position: relative;
    width: 100%;
    height: 280px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: contain; /* Changed from cover to contain for complete image display */
    object-position: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 1rem; /* Add padding to ensure images don't touch edges */
    box-sizing: border-box;
}

.professional-product-card:hover .product-image {
    transform: scale(1.1);
}

/* Image Loading States */
.product-image-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

@keyframes shimmer {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.image-placeholder {
    color: #6c757d;
    font-size: 3rem;
    opacity: 0.3;
}

.product-image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 0.9rem;
}

.product-image-error i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.5;
}

/* ==========================================================================
   Discount Badge
   ========================================================================== */

.professional-discount-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 700;
    z-index: 3;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
    transform: rotate(-5deg);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: rotate(-5deg) scale(1); }
    50% { transform: rotate(-5deg) scale(1.05); }
}

/* ==========================================================================
   Card Content
   ========================================================================== */

.professional-card-content {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.product-category {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-category i {
    font-size: 1rem;
}

.product-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-description {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* ==========================================================================
   Price Section
   ========================================================================== */

.professional-price-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    border: 1px solid rgba(0,0,0,0.05);
}

.price-display {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.current-price {
    font-size: 1.5rem;
    font-weight: 800;
    color: #28a745;
    margin-bottom: 0.25rem;
}

.original-price {
    font-size: 1rem;
    color: #6c757d;
    text-decoration: line-through;
    opacity: 0.7;
}

.discount-percentage {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* ==========================================================================
   Action Buttons
   ========================================================================== */

.professional-card-actions {
    display: flex;
    gap: 0.75rem;
}

.professional-btn {
    flex: 1;
    padding: 0.875rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.professional-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.professional-btn:hover::before {
    left: 100%;
}

.btn-view {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    border: 2px solid transparent;
}

.btn-view:hover {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    color: white;
}

.btn-add-cart {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 2px solid transparent;
}

.btn-add-cart:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-add-cart:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* ==========================================================================
   View All Products Button
   ========================================================================== */

.view-all-container {
    text-align: center;
    margin-top: 3rem;
}

.btn-view-all {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1.25rem 3rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 700;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-view-all::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s;
}

.btn-view-all:hover::before {
    left: 100%;
}

.btn-view-all:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-view-all i {
    font-size: 1.3rem;
    transition: transform 0.3s ease;
}

.btn-view-all:hover i {
    transform: scale(1.2);
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.products-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    flex-direction: column;
    gap: 1rem;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #6c757d;
    font-size: 1.1rem;
    font-weight: 500;
}

/* ==========================================================================
   Empty State
   ========================================================================== */

.products-empty {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.products-empty i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.products-empty h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #495057;
}

.products-empty p {
    font-size: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

/* ==========================================================================
   Responsive Design - Mobile First Approach
   ========================================================================== */

/* Large Tablets and Small Desktops */
@media (max-width: 1200px) {
    .featured-products-container {
        padding: 0 1.5rem;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 1.5rem;
    }

    .featured-products-title {
        font-size: 2.5rem;
    }
}

/* Tablets */
@media (max-width: 768px) {
    .featured-products-section {
        padding: 3rem 0;
        margin: 2rem 0;
    }

    .featured-products-container {
        padding: 0 1rem;
    }

    .featured-products-header {
        margin-bottom: 3rem;
    }

    .featured-products-title {
        font-size: 2.2rem;
        margin-bottom: 0.75rem;
    }

    .featured-products-subtitle {
        font-size: 1.1rem;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.25rem;
    }

    .product-image-container {
        height: 240px;
    }

    .professional-card-content {
        padding: 1.5rem;
    }

    .product-title {
        font-size: 1.25rem;
    }

    .professional-card-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .professional-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.85rem;
    }

    .btn-view-all {
        padding: 1rem 2.5rem;
        font-size: 1rem;
    }
}

/* Mobile Phones */
@media (max-width: 576px) {
    .featured-products-section {
        padding: 2rem 0;
        margin: 1.5rem 0;
    }

    .featured-products-container {
        padding: 0 0.75rem;
    }

    .featured-products-header {
        margin-bottom: 2.5rem;
    }

    .featured-products-title {
        font-size: 1.8rem;
        margin-bottom: 0.5rem;
    }

    .featured-products-subtitle {
        font-size: 1rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .professional-product-card {
        border-radius: 15px;
    }

    .product-image-container {
        height: 200px;
    }

    .professional-card-content {
        padding: 1.25rem;
    }

    .product-title {
        font-size: 1.1rem;
    }

    .product-description {
        font-size: 0.9rem;
        -webkit-line-clamp: 2;
    }

    .professional-price-section {
        padding: 0.75rem;
        flex-direction: column;
        align-items: flex-end;
        gap: 0.5rem;
    }

    .current-price {
        font-size: 1.3rem;
    }

    .professional-btn {
        padding: 0.625rem 1rem;
        font-size: 0.8rem;
    }

    .btn-view-all {
        padding: 0.875rem 2rem;
        font-size: 0.95rem;
    }

    .professional-discount-badge {
        top: 0.75rem;
        right: 0.75rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }
}

/* Extra Small Devices */
@media (max-width: 400px) {
    .featured-products-title {
        font-size: 1.6rem;
    }

    .featured-products-subtitle {
        font-size: 0.95rem;
    }

    .product-image-container {
        height: 180px;
    }

    .professional-card-content {
        padding: 1rem;
    }

    .product-title {
        font-size: 1rem;
    }

    .current-price {
        font-size: 1.2rem;
    }

    .btn-view-all {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* ==========================================================================
   Accessibility & Performance
   ========================================================================== */

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .professional-product-card,
    .product-image,
    .professional-btn,
    .btn-view-all,
    .professional-discount-badge {
        transition: none !important;
        animation: none !important;
    }

    .professional-product-card:hover {
        transform: none !important;
    }

    .product-image-loading {
        animation: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .professional-product-card {
        border: 2px solid #000;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .professional-btn {
        border: 2px solid #000;
    }

    .featured-products-title {
        text-shadow: none;
        color: #000;
    }
}

/* Focus styles for accessibility */
.professional-btn:focus,
.btn-view-all:focus {
    outline: 3px solid #667eea;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .featured-products-section {
        background: white !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }

    .professional-product-card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        break-inside: avoid;
    }

    .professional-card-actions {
        display: none !important;
    }

    .professional-discount-badge {
        background: #000 !important;
        color: white !important;
    }
}
